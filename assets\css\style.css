/* Portal Application Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #334155;
    background-color: #f8fafc;
    font-weight: 400;
}

/* Login Page Styles */
.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-container {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
}

.logo h1 {
    text-align: center;
    color: #667eea;
    margin-bottom: 2rem;
    font-size: 1.8rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #555;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s;
}

.btn-primary {
    background: linear-gradient(135deg, #2B5E5F 0%, #1e4a4b 100%);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1e4a4b 0%, #2B5E5F 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(43, 94, 95, 0.3);
}

.btn-secondary {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);
}

.btn-full {
    width: 100%;
}

.btn-sm {
    padding: 0.6rem 1.2rem;
    font-size: 0.875rem;
    border-radius: 12px;
    font-weight: 600;
}

/* Alert Styles */
.alert {
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-radius: 15px;
    border: 1px solid transparent;
    backdrop-filter: blur(10px);
    font-weight: 500;
}

.alert-error {
    background: rgba(248, 215, 218, 0.9);
    border-color: rgba(245, 198, 203, 0.8);
    color: #721c24;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.1);
}

.alert-success {
    background: rgba(212, 237, 218, 0.9);
    border-color: rgba(195, 230, 203, 0.8);
    color: #155724;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.1);
}

.alert-info {
    background: rgba(209, 236, 241, 0.9);
    border-color: rgba(190, 229, 235, 0.8);
    color: #0c5460;
    box-shadow: 0 4px 15px rgba(43, 94, 95, 0.1);
}

/* Dashboard Styles */
.dashboard {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
}

.dashboard::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover no-repeat;
    opacity: 0.03;
    z-index: -1;
    pointer-events: none;
}

.header {
    background: linear-gradient(135deg, #2B5E5F 0%, #1e4a4b 50%, #2B5E5F 100%);
    color: white;
    padding: 1.5rem 0;
    box-shadow: 0 8px 32px rgba(43, 94, 95, 0.3), 0 2px 8px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1), rgba(255,255,255,0.3));
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.header h1 {
    font-size: 1.8rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    letter-spacing: -0.5px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    font-size: 0.95rem;
}

.user-info .separator {
    color: rgba(255,255,255,0.5);
    font-weight: 300;
}

.user-info .role {
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    border: 1px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
}

.main-content {
    max-width: 1200px;
    margin: 3rem auto;
    padding: 0 2rem;
    position: relative;
    z-index: 1;
}

/* Welcome Section */
.welcome-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2.5rem;
    margin-bottom: 3rem;
    box-shadow: 0 10px 40px rgba(43, 94, 95, 0.1), 0 2px 8px rgba(0,0,0,0.05);
    border: 1px solid rgba(255,255,255,0.8);
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.8s ease-out;
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2B5E5F, #1e4a4b, #2B5E5F);
}

.welcome-section h2 {
    color: #2B5E5F;
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    letter-spacing: -0.5px;
}

.welcome-section p {
    color: #64748b;
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Module Grid */
.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2.5rem;
    margin-top: 2rem;
}

.module-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(43, 94, 95, 0.1), 0 2px 8px rgba(0,0,0,0.05);
    text-align: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    color: inherit;
    border: 1px solid rgba(255,255,255,0.8);
    position: relative;
    overflow: hidden;
}

.module-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2B5E5F, #1e4a4b, #2B5E5F);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.module-card:hover::before {
    transform: scaleX(1);
}

.module-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 60px rgba(43, 94, 95, 0.2), 0 8px 20px rgba(0,0,0,0.1);
}

.module-icon {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    color: #2B5E5F;
    transition: transform 0.3s ease;
}

.module-card:hover .module-icon {
    transform: scale(1.1);
}

.module-title {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 0.8rem;
    color: #2B5E5F;
    letter-spacing: -0.3px;
}

.module-description {
    color: #64748b;
    font-size: 1rem;
    line-height: 1.5;
}

/* Dashboard Stats Section */
.dashboard-stats {
    margin-top: 4rem;
}

.dashboard-stats h3 {
    color: #2B5E5F;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 2rem;
    text-align: center;
    letter-spacing: -0.3px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(43, 94, 95, 0.1), 0 2px 8px rgba(0,0,0,0.05);
    text-align: center;
    border: 1px solid rgba(255,255,255,0.8);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2B5E5F, #1e4a4b, #2B5E5F);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 50px rgba(43, 94, 95, 0.15), 0 5px 15px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 2.8rem;
    font-weight: 800;
    color: #2B5E5F;
    margin-bottom: 0.8rem;
    line-height: 1;
    text-shadow: 0 2px 4px rgba(43, 94, 95, 0.1);
}

.stat-label {
    color: #64748b;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Table Styles */
.table-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-top: 2rem;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #555;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Login Footer */
.login-footer {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
    text-align: center;
    font-size: 0.875rem;
    color: #666;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        padding: 0 1.5rem;
    }

    .modules-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        padding: 0 1.5rem;
    }

    .header h1 {
        font-size: 1.5rem;
    }

    .user-info {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
        font-size: 0.9rem;
    }

    .main-content {
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .welcome-section {
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .welcome-section h2 {
        font-size: 1.8rem;
    }

    .modules-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .module-card {
        padding: 2rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .stat-card {
        padding: 2rem;
    }

    .stat-number {
        font-size: 2.2rem;
    }

    .login-container {
        margin: 1rem;
        padding: 1.5rem;
    }

    .table-container {
        overflow-x: auto;
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 0 1rem;
    }

    .main-content {
        padding: 0 0.75rem;
    }

    .welcome-section {
        padding: 1.5rem;
    }

    .welcome-section h2 {
        font-size: 1.5rem;
    }

    .module-card {
        padding: 1.5rem;
    }

    .module-icon {
        font-size: 3rem;
    }

    .module-title {
        font-size: 1.2rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .dashboard-stats h3 {
        font-size: 1.5rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.module-card {
    animation: fadeInScale 0.6s ease-out forwards;
    opacity: 0;
}

.module-card:nth-child(1) { animation-delay: 0.1s; }
.module-card:nth-child(2) { animation-delay: 0.2s; }
.module-card:nth-child(3) { animation-delay: 0.3s; }
.module-card:nth-child(4) { animation-delay: 0.4s; }
.module-card:nth-child(5) { animation-delay: 0.5s; }

.stat-card {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

.stat-card:nth-child(1) { animation-delay: 0.6s; }
.stat-card:nth-child(2) { animation-delay: 0.7s; }
.stat-card:nth-child(3) { animation-delay: 0.8s; }

/* Admin Styles */
.form-section {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: normal;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.role-badge, .status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.75rem;
    text-transform: uppercase;
    font-weight: 600;
}

.role-admin {
    background-color: #dc3545;
    color: white;
}

.role-user {
    background-color: #28a745;
    color: white;
}

.status-active {
    background-color: #28a745;
    color: white;
}

.status-inactive {
    background-color: #6c757d;
    color: white;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #000;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
