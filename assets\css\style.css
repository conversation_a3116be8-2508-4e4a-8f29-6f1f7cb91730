/* Portal Application Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: #1e293b;
    background-color: #f8fafc;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Typography System */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
    color: #0f172a;
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }

.text-slate-900 { color: #0f172a; }
.text-slate-700 { color: #334155; }
.text-slate-600 { color: #475569; }
.text-slate-500 { color: #64748b; }
.text-slate-400 { color: #94a3b8; }
.text-slate-300 { color: #cbd5e1; }

/* Login Page Styles */
.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-container {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
}

.logo h1 {
    text-align: center;
    color: #667eea;
    margin-bottom: 2rem;
    font-size: 1.8rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #555;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s;
}

.btn-primary {
    background: linear-gradient(135deg, #2B5E5F 0%, #1e4a4b 100%);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1e4a4b 0%, #2B5E5F 100%);
    box-shadow: 0 4px 12px rgba(43, 94, 95, 0.2);
}

.btn-secondary {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255,255,255,0.3);
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

.btn-full {
    width: 100%;
}

.btn-sm {
    padding: 0.6rem 1.2rem;
    font-size: 0.875rem;
    border-radius: 12px;
    font-weight: 600;
}

/* Alert Styles */
.alert {
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-radius: 15px;
    border: 1px solid transparent;
    backdrop-filter: blur(10px);
    font-weight: 500;
}

.alert-error {
    background: rgba(248, 215, 218, 0.9);
    border-color: rgba(245, 198, 203, 0.8);
    color: #721c24;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.1);
}

.alert-success {
    background: rgba(212, 237, 218, 0.9);
    border-color: rgba(195, 230, 203, 0.8);
    color: #155724;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.1);
}

.alert-info {
    background: rgba(209, 236, 241, 0.9);
    border-color: rgba(190, 229, 235, 0.8);
    color: #0c5460;
    box-shadow: 0 4px 15px rgba(43, 94, 95, 0.1);
}

/* Dashboard Styles */
.dashboard {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
}

.dashboard::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover no-repeat;
    opacity: 0.03;
    z-index: -1;
    pointer-events: none;
}

.header {
    background: linear-gradient(135deg, #2B5E5F 0%, #1e4a4b 50%, #2B5E5F 100%);
    color: white;
    padding: 0;
    box-shadow: 0 8px 32px rgba(43, 94, 95, 0.3), 0 2px 8px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, rgba(255,255,255,0.4), rgba(255,255,255,0.1), rgba(255,255,255,0.4));
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
    min-height: 80px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 3rem;
}

.brand-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.brand-logo {
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-image {
    height: 48px;
    width: auto;
    max-width: 200px;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

.brand-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.brand-subtitle {
    font-size: 0.75rem;
    color: rgba(255,255,255,0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
}

.main-navigation {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border-radius: 12px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.nav-item:hover::before,
.nav-item.active::before {
    opacity: 1;
}

.nav-item:hover,
.nav-item.active {
    color: white;
    transform: translateY(-1px);
}

.nav-item.active {
    background: rgba(255,255,255,0.15);
    backdrop-filter: blur(10px);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.action-btn {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    padding: 0.75rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    backdrop-filter: blur(10px);
}

.action-btn:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.notification-btn {
    position: relative;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ef4444;
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.2rem 0.4rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    line-height: 1;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem;
    border-radius: 16px;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.user-avatar {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1rem;
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

.user-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.user-name {
    font-weight: 600;
    font-size: 0.95rem;
    color: white;
    line-height: 1.2;
}

.user-meta {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.8rem;
}

.client-name {
    color: rgba(255,255,255,0.7);
}

.role-badge {
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 0.25rem 0.6rem;
    border-radius: 8px;
    font-size: 0.7rem;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    border: 1px solid rgba(255,255,255,0.3);
}

.role-badge.admin {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: 1rem;
    padding-left: 1rem;
    border-left: 1px solid rgba(255,255,255,0.2);
}

.user-info .separator {
    color: rgba(255,255,255,0.5);
    font-weight: 300;
}

.user-info .role {
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    border: 1px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
}

/* Enhanced Layout System */
.main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 3rem 2rem 4rem;
    position: relative;
    z-index: 1;
    min-height: calc(100vh - 80px);
}

/* Spacing Utilities */
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }

.space-x-1 > * + * { margin-left: 0.25rem; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-x-8 > * + * { margin-left: 2rem; }

/* Container System */
.container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

.container-sm { max-width: 640px; }
.container-md { max-width: 768px; }
.container-lg { max-width: 1024px; }
.container-xl { max-width: 1280px; }
.container-2xl { max-width: 1536px; }

/* Grid System */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }
.gap-12 { gap: 3rem; }

/* Flexbox Utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-none { flex: none; }

/* Welcome Section */
.welcome-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2.5rem;
    margin-bottom: 3rem;
    box-shadow: 0 10px 40px rgba(43, 94, 95, 0.1), 0 2px 8px rgba(0,0,0,0.05);
    border: 1px solid rgba(255,255,255,0.8);
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.8s ease-out;
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2B5E5F, #1e4a4b, #2B5E5F);
}

.welcome-section h2 {
    color: #2B5E5F;
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    letter-spacing: -0.5px;
}

.welcome-section p {
    color: #64748b;
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Module Grid */
.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2.5rem;
}

.module-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    border-radius: 24px;
    box-shadow: 0 8px 25px rgba(43, 94, 95, 0.08), 0 2px 8px rgba(0,0,0,0.04);
    text-decoration: none;
    color: inherit;
    border: 1px solid rgba(255,255,255,0.9);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 280px;
}

.module-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--module-color, #2B5E5F), rgba(43, 94, 95, 0.5), var(--module-color, #2B5E5F));
}

.module-card:hover {
    box-shadow: 0 12px 35px rgba(43, 94, 95, 0.12), 0 4px 12px rgba(0,0,0,0.06);
}

.module-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 2rem 2rem 1rem;
    position: relative;
}

.module-icon {
    width: 64px;
    height: 64px;
    border-radius: 16px;
    background: linear-gradient(135deg, var(--module-color, #2B5E5F), rgba(43, 94, 95, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 8px 25px rgba(43, 94, 95, 0.2);
}

.module-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;
    position: relative;
}

.status-indicator.active::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: #10b981;
    opacity: 0.3;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(1.5); opacity: 0.1; }
}

.module-content {
    padding: 0 2rem;
    flex: 1;
}

.module-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: #1e293b;
    letter-spacing: -0.5px;
    line-height: 1.3;
}

.module-description {
    color: #64748b;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.module-footer {
    padding: 1.5rem 2rem 2rem;
    border-top: 1px solid rgba(226, 232, 240, 0.5);
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.module-stats {
    display: flex;
    gap: 1.5rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--module-color, #2B5E5F);
    line-height: 1;
}

.stat-label {
    font-size: 0.7rem;
    color: #94a3b8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
    text-align: center;
}

.module-action {
    width: 36px;
    height: 36px;
    border-radius: 10px;
    background: linear-gradient(135deg, var(--module-color, #2B5E5F), rgba(43, 94, 95, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    opacity: 0.9;
}

/* No ripple effects */

/* Dashboard Analytics Section */
.dashboard-analytics {
    margin-top: 4rem;
}

.analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2.5rem;
    padding: 0 0.5rem;
}

.analytics-header h3 {
    color: #1e293b;
    font-size: 2rem;
    font-weight: 700;
    letter-spacing: -0.5px;
    margin: 0;
}

.analytics-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.period-selector {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    color: #475569;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.period-selector:focus {
    outline: none;
    border-color: #2B5E5F;
    box-shadow: 0 0 0 3px rgba(43, 94, 95, 0.1);
}

.refresh-btn {
    background: linear-gradient(135deg, #2B5E5F, #1e4a4b);
    border: none;
    border-radius: 12px;
    padding: 0.75rem;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.refresh-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(43, 94, 95, 0.3);
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    box-shadow: 0 15px 50px rgba(43, 94, 95, 0.08), 0 5px 20px rgba(0,0,0,0.04);
    border: 1px solid rgba(255,255,255,0.9);
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
    display: flex;
    flex-direction: column;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--card-color);
}

.stat-card.primary {
    --card-color: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.stat-card.warning {
    --card-color: linear-gradient(90deg, #f59e0b, #d97706);
}

.stat-card.success {
    --card-color: linear-gradient(90deg, #10b981, #059669);
}

.stat-card.info {
    --card-color: linear-gradient(90deg, #2B5E5F, #1e4a4b);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 70px rgba(43, 94, 95, 0.12), 0 8px 25px rgba(0,0,0,0.06);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 2rem 2rem 1rem;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    background: var(--card-color);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-trend.positive {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.stat-trend.negative {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.stat-trend.neutral {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.stat-content {
    padding: 0 2rem 1.5rem;
    flex: 1;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1e293b;
    margin-bottom: 0.5rem;
    line-height: 1;
    text-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.stat-label {
    color: #475569;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    letter-spacing: -0.2px;
}

.stat-description {
    color: #94a3b8;
    font-size: 0.85rem;
    line-height: 1.4;
}

.stat-progress {
    height: 4px;
    background: rgba(226, 232, 240, 0.5);
    margin: 0 2rem 2rem;
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--card-color);
    border-radius: 2px;
    transition: width 1s ease;
}

/* Table Styles */
.table-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-top: 2rem;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #555;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Login Footer */
.login-footer {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
    text-align: center;
    font-size: 0.875rem;
    color: #666;
}

/* Enhanced Responsive Design */
@media (max-width: 1536px) {
    .header-content {
        max-width: 1200px;
    }

    .main-content {
        max-width: 1200px;
    }
}

@media (max-width: 1280px) {
    .header-content {
        max-width: 1024px;
    }

    .main-content {
        max-width: 1024px;
        padding: 2.5rem 1.5rem 3.5rem;
    }

    .modules-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 1.5rem;
    }

    .analytics-grid {
        grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 1024px) {
    .header-left {
        gap: 2rem;
    }

    .main-navigation {
        display: none;
    }

    .header-content {
        max-width: 768px;
        padding: 1rem 1.5rem;
    }

    .main-content {
        max-width: 768px;
        padding: 2rem 1.5rem 3rem;
    }

    .modules-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
    }

    .analytics-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 1.5rem;
    }

    .analytics-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .analytics-controls {
        align-self: stretch;
        justify-content: space-between;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        padding: 1rem;
        min-height: auto;
    }

    .header-left {
        width: 100%;
        justify-content: center;
    }

    .header-right {
        width: 100%;
        justify-content: center;
    }

    .brand-text h1 {
        font-size: 1.4rem;
    }

    .user-profile {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }

    .user-actions {
        margin-left: 0;
        padding-left: 0;
        border-left: none;
        border-top: 1px solid rgba(255,255,255,0.2);
        padding-top: 1rem;
        margin-top: 1rem;
    }

    .main-content {
        padding: 1.5rem 1rem 2.5rem;
    }

    .welcome-section {
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
    }

    .welcome-section h2 {
        font-size: 1.75rem;
    }

    .modules-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin-top: 2rem;
    }

    .module-card {
        min-height: 240px;
    }

    .module-header {
        padding: 1.5rem 1.5rem 1rem;
    }

    .module-content {
        padding: 0 1.5rem;
    }

    .module-footer {
        padding: 1rem 1.5rem 1.5rem;
    }

    .module-stats {
        gap: 1rem;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .stat-header {
        padding: 1.5rem 1.5rem 1rem;
    }

    .stat-content {
        padding: 0 1.5rem 1rem;
    }

    .stat-progress {
        margin: 0 1.5rem 1.5rem;
    }

    .analytics-header h3 {
        font-size: 1.75rem;
    }
}

@media (max-width: 640px) {
    .header-content {
        padding: 0.75rem;
    }

    .brand-section {
        gap: 0.75rem;
    }

    .brand-text h1 {
        font-size: 1.25rem;
    }

    .brand-subtitle {
        font-size: 0.7rem;
    }

    .header-actions {
        gap: 0.75rem;
    }

    .action-btn {
        padding: 0.6rem;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        font-size: 0.9rem;
    }

    .main-content {
        padding: 1rem 0.75rem 2rem;
    }

    .welcome-section {
        padding: 1.5rem;
        border-radius: 16px;
    }

    .welcome-section h2 {
        font-size: 1.5rem;
    }

    .module-card {
        border-radius: 16px;
        min-height: 220px;
    }

    .module-header {
        padding: 1.25rem 1.25rem 0.75rem;
    }

    .module-icon {
        width: 56px;
        height: 56px;
    }

    .module-content {
        padding: 0 1.25rem;
    }

    .module-title {
        font-size: 1.25rem;
    }

    .module-description {
        font-size: 0.9rem;
    }

    .module-footer {
        padding: 0.75rem 1.25rem 1.25rem;
    }

    .stat-card {
        border-radius: 16px;
    }

    .stat-number {
        font-size: 2rem;
    }

    .analytics-header h3 {
        font-size: 1.5rem;
    }

    .period-selector {
        font-size: 0.85rem;
        padding: 0.6rem 0.8rem;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 0.75rem 0.5rem 1.5rem;
    }

    .welcome-section {
        padding: 1.25rem;
        margin-bottom: 1.5rem;
    }

    .welcome-section h2 {
        font-size: 1.375rem;
    }

    .modules-grid {
        gap: 1.25rem;
    }

    .analytics-grid {
        gap: 1.25rem;
    }

    .analytics-header {
        margin-bottom: 2rem;
    }

    .analytics-controls {
        flex-direction: column;
        gap: 0.75rem;
    }
}

/* Advanced Animations & Micro-interactions */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Simplified animations - only keep essential ones */

/* Loading States */
.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Enhanced Hover Effects */
.interactive-element {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.interactive-element:hover {
    transform: translateY(-2px);
}

.interactive-element:active {
    transform: translateY(0);
    transition: all 0.1s;
}

/* No staggered animations */

/* Module cards - no animations */

.stat-card {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

.stat-card:nth-child(1) { animation-delay: 0.6s; }
.stat-card:nth-child(2) { animation-delay: 0.7s; }
.stat-card:nth-child(3) { animation-delay: 0.8s; }
.stat-card:nth-child(4) { animation-delay: 0.9s; }

/* Advanced Button Interactions */
.btn {
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* Enhanced Focus States */
.btn:focus,
.nav-item:focus,
.action-btn:focus,
.period-selector:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(43, 94, 95, 0.2);
}

/* Icons - no floating animation */

/* No glow effects */

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Simple Transitions */
* {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.3s ease;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.spinner-ring {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(43, 94, 95, 0.1);
    border-top: 4px solid #2B5E5F;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #2B5E5F;
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(226, 232, 240, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #2B5E5F, #1e4a4b);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #1e4a4b, #2B5E5F);
}

/* Selection Styling */
::selection {
    background: rgba(43, 94, 95, 0.2);
    color: #1e293b;
}

::-moz-selection {
    background: rgba(43, 94, 95, 0.2);
    color: #1e293b;
}

/* Focus Visible Support */
.btn:focus-visible,
.nav-item:focus-visible,
.action-btn:focus-visible {
    outline: 2px solid #2B5E5F;
    outline-offset: 2px;
}

/* Invoice Cards Styling */
.invoices-container {
    margin-top: 2rem;
}

.invoices-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 0 0.5rem;
}

.invoices-header h3 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.invoices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
}

.invoice-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    box-shadow: 0 15px 50px rgba(43, 94, 95, 0.08), 0 5px 20px rgba(0,0,0,0.04);
    border: 1px solid rgba(255,255,255,0.9);
    overflow: hidden;
    transition: all 0.4s ease;
    position: relative;
}

.invoice-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--status-color, #2B5E5F);
}

.invoice-card[data-status="paid"]::before,
.invoice-card[data-status="completed"]::before {
    background: linear-gradient(90deg, #10b981, #059669);
}

.invoice-card[data-status="pending"]::before {
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

.invoice-card[data-status="overdue"]::before {
    background: linear-gradient(90deg, #ef4444, #dc2626);
}

.invoice-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 70px rgba(43, 94, 95, 0.12), 0 8px 25px rgba(0,0,0,0.06);
}

.invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 2rem 2rem 1rem;
}

.invoice-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #2B5E5F, #1e4a4b);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 8px 20px rgba(43, 94, 95, 0.2);
}

.invoice-status {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-badge.status-success {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-badge.status-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-badge.status-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-badge.status-secondary {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
    border: 1px solid rgba(107, 114, 128, 0.2);
}

.invoice-content {
    padding: 0 2rem 1.5rem;
}

.invoice-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.invoice-description {
    color: #64748b;
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 1.5rem;
}

.invoice-details {
    display: grid;
    gap: 0.75rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detail-label {
    color: #94a3b8;
    font-size: 0.85rem;
    font-weight: 500;
}

.detail-value {
    color: #475569;
    font-weight: 600;
    font-size: 0.9rem;
}

.detail-value.amount {
    color: #2B5E5F;
    font-size: 1.1rem;
    font-weight: 700;
}

.detail-value.due-date {
    color: #f59e0b;
}

.invoice-actions {
    padding: 1.5rem 2rem 2rem;
    border-top: 1px solid rgba(226, 232, 240, 0.5);
    display: flex;
    gap: 1rem;
}

.btn-icon {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    justify-content: center;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-icon:hover {
    transform: translateY(-2px);
}

.btn-icon.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Invoice Table Styling */
.invoices-table-container {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    box-shadow: 0 15px 50px rgba(43, 94, 95, 0.08), 0 5px 20px rgba(0,0,0,0.04);
    border: 1px solid rgba(255,255,255,0.9);
    overflow: hidden;
    margin-top: 2rem;
}

.invoices-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95rem;
}

.invoices-table thead {
    background: linear-gradient(135deg, #2B5E5F, #1e4a4b);
    color: white;
}

.invoices-table th {
    padding: 1.25rem 1rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid rgba(255,255,255,0.2);
}

.invoices-table tbody tr {
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    transition: background-color 0.2s ease;
}

.invoices-table tbody tr:hover {
    background: rgba(43, 94, 95, 0.02);
}

.invoices-table td {
    padding: 1.25rem 1rem;
    vertical-align: middle;
}

.invoice-number {
    color: #2B5E5F;
    font-weight: 600;
}

.invoice-description {
    color: #64748b;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.invoice-amount {
    color: #2B5E5F;
    font-weight: 700;
    font-size: 1.05rem;
}

.invoice-date,
.invoice-due-date {
    color: #475569;
    font-size: 0.9rem;
}

.invoice-status .status-badge {
    padding: 0.4rem 0.8rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.action-buttons .btn {
    font-size: 0.8rem;
    padding: 0.5rem 0.8rem;
    border-radius: 8px;
    font-weight: 500;
}

/* Responsive Table */
@media (max-width: 1024px) {
    .invoices-table-container {
        overflow-x: auto;
    }

    .invoices-table {
        min-width: 800px;
    }

    .invoice-description {
        max-width: 150px;
    }
}

@media (max-width: 768px) {
    .invoices-table {
        min-width: 700px;
    }

    .invoices-table th,
    .invoices-table td {
        padding: 1rem 0.75rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }

    .action-buttons .btn {
        font-size: 0.75rem;
        padding: 0.4rem 0.6rem;
    }
}

/* Print Styles */
@media print {
    .header,
    .loading-overlay,
    .action-btn,
    .refresh-btn {
        display: none !important;
    }

    .main-content {
        max-width: none;
        padding: 0;
        margin: 0;
    }

    .module-card,
    .stat-card,
    .invoice-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #e2e8f0;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .loading-overlay {
        display: none !important;
    }
}

/* Admin Styles */
.form-section {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: normal;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.role-badge, .status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.75rem;
    text-transform: uppercase;
    font-weight: 600;
}

.role-admin {
    background-color: #dc3545;
    color: white;
}

.role-user {
    background-color: #28a745;
    color: white;
}

.status-active {
    background-color: #28a745;
    color: white;
}

.status-inactive {
    background-color: #6c757d;
    color: white;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #000;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
