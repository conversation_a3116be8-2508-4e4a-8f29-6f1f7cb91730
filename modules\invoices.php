<?php
/**
 * Invoices Module
 * Display and manage client invoices from FTP
 */

require_once '../config/config.php';
require_once '../classes/User.php';
require_once '../classes/Client.php';
require_once '../classes/FTPManager.php';

// Require login and invoices module access
requireLogin();

$database = new Database();
$db = $database->getConnection();
$user = new User($db);
$client = new Client($db);

// Check if user has access to invoices module
$user_rights = $user->getUserRights($_SESSION['user_id']);
if (!in_array('invoices', $user_rights)) {
    header('Location: ../dashboard.php');
    exit();
}

$error_message = '';
$success_message = '';
$invoices = [];

// Get client's FTP folder path
$client_data = $client->getById($_SESSION['client_id']);
if (!$client_data) {
    $error_message = 'Client data not found.';
} else {
    try {
        $ftp = new FTPManager();
        $ftp->connect();

        // Get invoice files from client's FTP folder
        $invoices = $ftp->getClientFiles($client_data['ftp_folder_path']);

        $ftp->disconnect();
    } catch (Exception $e) {
        $error_message = 'FTP server is not configured. This is a demo environment - FTP functionality is disabled.';
        error_log('FTP Error: ' . $e->getMessage());

        // For demo purposes, create some sample invoice data with enhanced details
        $invoices = [
            [
                'filename' => 'sample_invoice_001.pdf',
                'invoice_number' => 'INV-2024-001',
                'amount' => 1250.00,
                'date' => '2024-01-15',
                'due_date' => '2024-02-15',
                'status' => 'pending',
                'description' => 'Professional Services - January 2024'
            ],
            [
                'filename' => 'sample_invoice_002.pdf',
                'invoice_number' => 'INV-2024-002',
                'amount' => 2750.50,
                'date' => '2024-01-20',
                'due_date' => '2024-02-20',
                'status' => 'overdue',
                'description' => 'Freight Services - Shipment #FS-001'
            ],
            [
                'filename' => 'sample_invoice_003.pdf',
                'invoice_number' => 'INV-2024-003',
                'amount' => 890.25,
                'date' => '2024-01-25',
                'due_date' => '2024-02-25',
                'status' => 'paid',
                'description' => 'Customs Clearance Services'
            ]
        ];
    }
}

// Handle file download
if (isset($_GET['download']) && !empty($_GET['download'])) {
    $filename = sanitizeInput($_GET['download']);
    
    // Verify file exists in our list (security check)
    $file_found = false;
    foreach ($invoices as $invoice) {
        if ($invoice['filename'] === $filename) {
            $file_found = true;
            break;
        }
    }
    
    if ($file_found) {
        try {
            $ftp = new FTPManager();
            $ftp->connect();
            
            $remote_file = rtrim($client_data['ftp_folder_path'], '/') . '/' . $filename;
            $temp_file = tempnam(sys_get_temp_dir(), 'invoice_');
            
            if ($ftp->downloadFile($remote_file, $temp_file)) {
                // Set headers for file download
                header('Content-Type: application/pdf');
                header('Content-Disposition: attachment; filename="' . $filename . '"');
                header('Content-Length: ' . filesize($temp_file));
                
                // Output file
                readfile($temp_file);
                
                // Clean up
                unlink($temp_file);
                $ftp->disconnect();
                exit();
            } else {
                $error_message = 'Failed to download file.';
            }
            
            $ftp->disconnect();
        } catch (Exception $e) {
            // FTP failed, create demo PDF for demonstration
            error_log('FTP Download Error: ' . $e->getMessage());

            // Create a simple demo PDF content
            $pdf_content = "%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents 4 0 R\n>>\nendobj\n4 0 obj\n<<\n/Length 44\n>>\nstream\nBT\n/F1 12 Tf\n100 700 Td\n(Demo Invoice: " . $filename . ") Tj\nET\nendstream\nendobj\nxref\n0 5\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \n0000000206 00000 n \ntrailer\n<<\n/Size 5\n/Root 1 0 R\n>>\nstartxref\n299\n%%EOF";

            // Set headers for PDF download
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Content-Length: ' . strlen($pdf_content));

            // Output demo PDF
            echo $pdf_content;
            exit();
        }
    } else {
        $error_message = 'File not found or access denied.';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoices - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="dashboard">
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <div class="brand-section">
                    <div class="brand-logo">
                        <img src="../assets/img/logo.png" alt="<?php echo APP_NAME; ?>" class="logo-image">
                    </div>
                    <div class="brand-text">
                        <span class="brand-subtitle">Invoices Module</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <!-- Main Website Button -->
                    <a href="https://www.advancedcustomssolutions.com" target="_blank" class="action-btn website-btn" title="Visit Main Website">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10 2C5.58 2 2 5.58 2 10s3.58 8 8 8 8-3.58 8-8-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6-2.69 6-6 6z"/>
                            <path d="M10 6v4l3 3"/>
                        </svg>
                        <span class="btn-text">Main Website</span>
                    </a>

                    <a href="../dashboard.php" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                    <a href="../logout.php" class="btn btn-sm btn-danger">Logout</a>
                </div>
            </div>
        </div>
    </header>
    
    <main class="main-content">
        <div class="module-header">
            <h2>📄 Invoices</h2>
            <p>View and manage your invoices. Click download to save a copy or pay now to make a payment.</p>
        </div>
        
        <?php if ($error_message): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <div class="invoices-section">
            <?php if (empty($invoices)): ?>
                <div class="empty-state">
                    <div class="empty-icon">📄</div>
                    <h3>No Invoices Found</h3>
                    <p>There are currently no invoice files available for your account.</p>
                    <p>Invoice files will appear here once they are uploaded to your client folder.</p>
                </div>
            <?php else: ?>
                <div class="invoices-container">
                    <div class="invoices-header">
                        <h3>Available Invoices (<?php echo count($invoices); ?>)</h3>
                        <div class="header-actions">
                            <button onclick="refreshInvoices()" class="btn btn-sm btn-secondary">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                    <path d="M8 3a5 5 0 104.546 2.914.5.5 0 00-.908-.417A4 4 0 118 4v1z"/>
                                </svg>
                                Refresh
                            </button>
                        </div>
                    </div>

                    <!-- Table View -->
                    <div class="invoices-table-container">
                        <table class="invoices-table">
                            <thead>
                                <tr>
                                    <th>Invoice Number</th>
                                    <th>Description</th>
                                    <th>Amount</th>
                                    <th>Date</th>
                                    <th>Due Date</th>
                                    <th>Status</th>
                                    <th>Download PDF</th>
                                    <th>Payment</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($invoices as $invoice): ?>
                                    <?php
                                    // Handle both array and string invoice data
                                    if (is_string($invoice)) {
                                        // Convert string filename to array format for demo
                                        $invoice = [
                                            'filename' => $invoice,
                                            'invoice_number' => 'DEMO-' . substr($invoice, -7, 3),
                                            'amount' => rand(500, 5000),
                                            'date' => date('Y-m-d', strtotime('-' . rand(1, 30) . ' days')),
                                            'due_date' => date('Y-m-d', strtotime('+' . rand(1, 30) . ' days')),
                                            'status' => ['pending', 'paid', 'overdue'][rand(0, 2)],
                                            'description' => 'Demo invoice for ' . pathinfo($invoice, PATHINFO_FILENAME)
                                        ];
                                    }

                                    // Check payment status
                                    $payment_status = $invoice['status'] ?? 'pending';
                                    $stmt = $db->prepare("SELECT payment_status FROM payments WHERE invoice_name = ? AND client_id = ? ORDER BY created_at DESC LIMIT 1");
                                    $stmt->execute([$invoice['filename'], $_SESSION['client_id']]);
                                    $payment = $stmt->fetch();
                                    if ($payment) {
                                        $payment_status = $payment['payment_status'];
                                    }

                                    // Determine status styling
                                    $status_class = '';
                                    $status_icon = '';
                                    switch ($payment_status) {
                                        case 'completed':
                                        case 'paid':
                                            $status_class = 'success';
                                            $status_icon = '✅';
                                            break;
                                        case 'pending':
                                            $status_class = 'warning';
                                            $status_icon = '⏳';
                                            break;
                                        case 'overdue':
                                            $status_class = 'danger';
                                            $status_icon = '⚠️';
                                            break;
                                        default:
                                            $status_class = 'secondary';
                                            $status_icon = '📄';
                                    }
                                    ?>
                                    <tr class="invoice-row">
                                        <td class="invoice-number">
                                            <strong><?php echo htmlspecialchars($invoice['invoice_number'] ?? 'N/A'); ?></strong>
                                        </td>
                                        <td class="invoice-description">
                                            <?php echo htmlspecialchars($invoice['description'] ?? $invoice['filename']); ?>
                                        </td>
                                        <td class="invoice-amount">
                                            <strong>R<?php echo number_format($invoice['amount'] ?? 0, 2); ?></strong>
                                        </td>
                                        <td class="invoice-date">
                                            <?php echo date('d M Y', strtotime($invoice['date'] ?? 'now')); ?>
                                        </td>
                                        <td class="invoice-due-date">
                                            <?php echo date('d M Y', strtotime($invoice['due_date'] ?? '+30 days')); ?>
                                        </td>
                                        <td class="invoice-status">
                                            <span class="status-badge status-<?php echo $status_class; ?>">
                                                <?php echo $status_icon; ?> <?php echo ucfirst($payment_status); ?>
                                            </span>
                                        </td>
                                        <td class="invoice-download">
                                            <a href="?download=<?php echo urlencode($invoice['filename']); ?>"
                                               class="btn btn-sm btn-secondary" title="Download PDF">
                                                📥 Download
                                            </a>
                                        </td>
                                        <td class="invoice-payment">
                                            <?php if ($payment_status !== 'completed' && $payment_status !== 'paid'): ?>
                                                <a href="payment.php?invoice=<?php echo urlencode($invoice['filename']); ?>&amount=<?php echo $invoice['amount'] ?? 0; ?>"
                                                   class="btn btn-sm btn-primary" title="Pay Invoice">
                                                    💳 Pay Now
                                                </a>
                                            <?php else: ?>
                                                <span class="btn btn-sm btn-success disabled" title="Already Paid">
                                                    ✅ Paid
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>


                </div>
            <?php endif; ?>
        </div>
        
        <div class="invoice-dashboard">
            <div class="dashboard-header">
                <h3>Invoice Dashboard</h3>
                <div class="dashboard-actions">
                    <button class="action-btn refresh-btn" onclick="refreshInvoices()" title="Refresh Data">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8 3a5 5 0 104.546 2.914.5.5 0 00-.908-.417A4 4 0 118 4v1z"/>
                            <path d="M8 4.466V.534a.25.25 0 01.41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 018 4.466z"/>
                        </svg>
                        Refresh
                    </button>
                </div>
            </div>

            <div class="dashboard-grid">
                <!-- FTP Connection Status -->
                <div class="dashboard-card connection-card" onclick="toggleConnectionDetails()">
                    <div class="card-header">
                        <div class="card-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M3 3h18v18H3V3zm2 2v14h14V5H5zm2 2h10v2H7V7zm0 4h10v2H7v-2zm0 4h7v2H7v-2z"/>
                            </svg>
                        </div>
                        <div class="card-title">📁 Client Folder</div>
                        <div class="card-status">
                            <span class="status-indicator demo"></span>
                            Demo Mode
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="folder-path">
                            <span class="path-label">Path:</span>
                            <span class="path-value"><?php echo htmlspecialchars($client_data['ftp_folder_path']); ?></span>
                        </div>
                        <div class="connection-details" id="connectionDetails" style="display: none;">
                            <div class="detail-item">
                                <span class="detail-label">Server Status:</span>
                                <span class="detail-value warning">Demo Mode Active</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Last Sync:</span>
                                <span class="detail-value">Using sample data</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Available Files:</span>
                                <span class="detail-value"><?php echo count($invoices); ?> documents</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-secondary" onclick="event.stopPropagation(); showToast('FTP sync feature coming soon!', 'info')">
                            🔄 Sync Files
                        </button>
                    </div>
                </div>

                <!-- Payment Summary -->
                <div class="dashboard-card payment-card">
                    <div class="card-header">
                        <div class="card-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M2 4a2 2 0 012-2h16a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2V4zm2 0v4h16V4H4zm0 8a2 2 0 012-2h16a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4zm2 0v4h16v-4H4z"/>
                            </svg>
                        </div>
                        <div class="card-title">📊 Payment Summary</div>
                        <div class="card-status">
                            <span class="status-indicator <?php echo $unpaid_count > 0 ? 'warning' : 'success'; ?>"></span>
                            <?php echo $unpaid_count > 0 ? 'Action Required' : 'All Paid'; ?>
                        </div>
                    </div>
                    <div class="card-content">
                        <?php
                        $paid_count = 0;
                        $unpaid_count = 0;
                        $total_paid = 0;
                        $total_unpaid = 0;

                        foreach ($invoices as $invoice) {
                            $stmt = $db->prepare("SELECT payment_status, amount FROM payments WHERE invoice_name = ? AND client_id = ? ORDER BY created_at DESC LIMIT 1");
                            $stmt->execute([$invoice['filename'], $_SESSION['client_id']]);
                            $payment = $stmt->fetch();
                            $status = $payment ? $payment['payment_status'] : 'unpaid';
                            $amount = $invoice['amount'] ?? rand(500, 5000);

                            if ($status === 'completed') {
                                $paid_count++;
                                $total_paid += $amount;
                            } else {
                                $unpaid_count++;
                                $total_unpaid += $amount;
                            }
                        }
                        ?>
                        <div class="payment-stats">
                            <div class="stat-item paid" onclick="filterInvoices('paid')">
                                <div class="stat-number"><?php echo $paid_count; ?></div>
                                <div class="stat-label">Paid Invoices</div>
                                <div class="stat-amount">R<?php echo number_format($total_paid, 2); ?></div>
                            </div>
                            <div class="stat-divider"></div>
                            <div class="stat-item unpaid" onclick="filterInvoices('unpaid')">
                                <div class="stat-number"><?php echo $unpaid_count; ?></div>
                                <div class="stat-label">Unpaid Invoices</div>
                                <div class="stat-amount">R<?php echo number_format($total_unpaid, 2); ?></div>
                            </div>
                        </div>
                        <div class="payment-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: <?php echo $paid_count > 0 ? ($paid_count / ($paid_count + $unpaid_count)) * 100 : 0; ?>%"></div>
                            </div>
                            <div class="progress-label">
                                <?php echo $paid_count > 0 ? round(($paid_count / ($paid_count + $unpaid_count)) * 100) : 0; ?>% Paid
                            </div>
                        </div>
                    </div>
                    <div class="card-actions">
                        <?php if ($unpaid_count > 0): ?>
                            <button class="btn btn-sm btn-primary" onclick="payAllUnpaid()">
                                💳 Pay All Outstanding
                            </button>
                        <?php else: ?>
                            <button class="btn btn-sm btn-success disabled" disabled>
                                ✅ All Invoices Paid
                            </button>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-card actions-card">
                    <div class="card-header">
                        <div class="card-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <div class="card-title">⚡ Quick Actions</div>
                    </div>
                    <div class="card-content">
                        <div class="action-list">
                            <div class="action-item" onclick="downloadAllPDFs()">
                                <div class="action-icon">📥</div>
                                <div class="action-info">
                                    <div class="action-name">Download All PDFs</div>
                                    <div class="action-desc">Bulk download all invoices</div>
                                </div>
                                <div class="action-arrow">→</div>
                            </div>
                            <div class="action-item" onclick="exportToCSV()">
                                <div class="action-icon">📊</div>
                                <div class="action-info">
                                    <div class="action-name">Export to CSV</div>
                                    <div class="action-desc">Download invoice data</div>
                                </div>
                                <div class="action-arrow">→</div>
                            </div>
                            <div class="action-item" onclick="printInvoiceList()">
                                <div class="action-icon">🖨️</div>
                                <div class="action-info">
                                    <div class="action-name">Print Invoice List</div>
                                    <div class="action-desc">Print summary report</div>
                                </div>
                                <div class="action-arrow">→</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <script src="../assets/js/script.js"></script>
    <script>
        function refreshInvoices() {
            showToast('Refreshing invoice data...', 'info');
            setTimeout(() => {
                window.location.reload();
            }, 500);
        }

        function toggleConnectionDetails() {
            const details = document.getElementById('connectionDetails');
            const card = document.querySelector('.connection-card');

            if (details.style.display === 'none') {
                details.style.display = 'block';
                card.classList.add('expanded');
            } else {
                details.style.display = 'none';
                card.classList.remove('expanded');
            }
        }

        function filterInvoices(type) {
            const rows = document.querySelectorAll('.invoice-row');
            let visibleCount = 0;

            rows.forEach(row => {
                const statusBadge = row.querySelector('.status-badge');
                const isPaid = statusBadge.textContent.toLowerCase().includes('paid');

                if (type === 'paid' && isPaid) {
                    row.style.display = 'table-row';
                    visibleCount++;
                } else if (type === 'unpaid' && !isPaid) {
                    row.style.display = 'table-row';
                    visibleCount++;
                } else if (type === 'all') {
                    row.style.display = 'table-row';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });

            showToast(`Showing ${visibleCount} ${type} invoices`, 'success');

            // Add filter reset button
            if (type !== 'all') {
                setTimeout(() => {
                    const resetBtn = document.createElement('button');
                    resetBtn.className = 'btn btn-sm btn-secondary';
                    resetBtn.textContent = '🔄 Show All';
                    resetBtn.onclick = () => {
                        filterInvoices('all');
                        resetBtn.remove();
                    };
                    resetBtn.style.position = 'fixed';
                    resetBtn.style.top = '100px';
                    resetBtn.style.right = '20px';
                    resetBtn.style.zIndex = '1000';
                    document.body.appendChild(resetBtn);

                    setTimeout(() => {
                        if (resetBtn.parentNode) {
                            resetBtn.remove();
                        }
                    }, 5000);
                }, 1000);
            }
        }

        function payAllUnpaid() {
            const unpaidRows = document.querySelectorAll('.invoice-row');
            const unpaidInvoices = [];

            unpaidRows.forEach(row => {
                const statusBadge = row.querySelector('.status-badge');
                const isPaid = statusBadge.textContent.toLowerCase().includes('paid');

                if (!isPaid) {
                    const payButton = row.querySelector('.btn-primary');
                    if (payButton) {
                        unpaidInvoices.push(payButton.href);
                    }
                }
            });

            if (unpaidInvoices.length > 0) {
                const totalAmount = unpaidInvoices.length * 1500; // Approximate
                const confirmPay = confirm(`Pay all ${unpaidInvoices.length} outstanding invoices?\\nEstimated total: R${totalAmount.toLocaleString()}`);

                if (confirmPay) {
                    showToast('Redirecting to bulk payment...', 'info');
                    // For demo, redirect to first unpaid invoice
                    setTimeout(() => {
                        window.location.href = unpaidInvoices[0];
                    }, 1000);
                }
            } else {
                showToast('No unpaid invoices found', 'info');
            }
        }

        function downloadAllPDFs() {
            const downloadButtons = document.querySelectorAll('.invoice-download .btn');
            let downloadCount = 0;

            showToast('Starting bulk download...', 'info');

            downloadButtons.forEach((button, index) => {
                setTimeout(() => {
                    button.click();
                    downloadCount++;

                    if (downloadCount === downloadButtons.length) {
                        showToast(`Downloaded ${downloadCount} PDF files`, 'success');
                    }
                }, index * 500); // Stagger downloads
            });
        }

        function exportToCSV() {
            const rows = document.querySelectorAll('.invoice-row');
            let csvContent = 'Invoice Number,Description,Amount,Date,Due Date,Status\\n';

            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                const rowData = [
                    cells[0].textContent.trim(),
                    cells[1].textContent.trim(),
                    cells[2].textContent.trim(),
                    cells[3].textContent.trim(),
                    cells[4].textContent.trim(),
                    cells[5].textContent.trim().replace(/[^a-zA-Z]/g, '')
                ];
                csvContent += rowData.join(',') + '\\n';
            });

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'invoices_' + new Date().toISOString().split('T')[0] + '.csv';
            a.click();
            window.URL.revokeObjectURL(url);

            showToast('Invoice data exported to CSV', 'success');
        }

        function printInvoiceList() {
            const printWindow = window.open('', '_blank');
            const tableHTML = document.querySelector('.invoices-table-container').outerHTML;

            printWindow.document.write(`
                <html>
                <head>
                    <title>Invoice List - ${new Date().toLocaleDateString()}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f2f2f2; }
                        .status-badge { padding: 4px 8px; border-radius: 4px; }
                        .status-success { background: #d4edda; color: #155724; }
                        .status-warning { background: #fff3cd; color: #856404; }
                        .status-danger { background: #f8d7da; color: #721c24; }
                    </style>
                </head>
                <body>
                    <h1>Invoice List</h1>
                    <p>Generated on: ${new Date().toLocaleString()}</p>
                    ${tableHTML}
                </body>
                </html>
            `);

            printWindow.document.close();
            printWindow.print();

            showToast('Print dialog opened', 'success');
        }

        // Show toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;

            // Add toast to page
            document.body.appendChild(toast);

            // Show toast
            setTimeout(() => toast.classList.add('show'), 100);

            // Remove toast after 3 seconds
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // Initialize dashboard interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects to dashboard cards
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Add click effects to stat items
            const statItems = document.querySelectorAll('.stat-item');
            statItems.forEach(item => {
                item.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>

<?php
/**
 * Format file size
 */
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>

<style>
.module-header {
    margin-bottom: 2rem;
}

.module-header h2 {
    color: #333;
    margin-bottom: 0.5rem;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-state h3 {
    color: #666;
    margin-bottom: 1rem;
}

.empty-state p {
    color: #888;
    margin-bottom: 0.5rem;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px 10px 0 0;
}

.invoice-info {
    display: flex;
    flex-direction: column;
}

.payment-status {
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.75rem;
    text-transform: uppercase;
    font-weight: 600;
}

.status-completed {
    background-color: #28a745;
    color: white;
}

.status-pending {
    background-color: #ffc107;
    color: #212529;
}

.status-unpaid {
    background-color: #dc3545;
    color: white;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.invoice-info-section {
    margin-top: 2rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.info-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-card h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.info-card p {
    color: #666;
    margin: 0;
}
</style>
