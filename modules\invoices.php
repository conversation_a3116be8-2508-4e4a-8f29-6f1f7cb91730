<?php
/**
 * Invoices Module
 * Display and manage client invoices from FTP
 */

require_once '../config/config.php';
require_once '../classes/User.php';
require_once '../classes/Client.php';
require_once '../classes/FTPManager.php';

// Require login and invoices module access
requireLogin();

$database = new Database();
$db = $database->getConnection();
$user = new User($db);
$client = new Client($db);

// Check if user has access to invoices module
$user_rights = $user->getUserRights($_SESSION['user_id']);
if (!in_array('invoices', $user_rights)) {
    header('Location: ../dashboard.php');
    exit();
}

$error_message = '';
$success_message = '';
$invoices = [];

// Get client's FTP folder path
$client_data = $client->getById($_SESSION['client_id']);
if (!$client_data) {
    $error_message = 'Client data not found.';
} else {
    try {
        $ftp = new FTPManager();
        $ftp->connect();
        
        // Get invoice files from client's FTP folder
        $invoices = $ftp->getClientFiles($client_data['ftp_folder_path']);
        
        $ftp->disconnect();
    } catch (Exception $e) {
        $error_message = 'Unable to connect to file server: ' . $e->getMessage();
        error_log('FTP Error: ' . $e->getMessage());
    }
}

// Handle file download
if (isset($_GET['download']) && !empty($_GET['download'])) {
    $filename = sanitizeInput($_GET['download']);
    
    // Verify file exists in our list (security check)
    $file_found = false;
    foreach ($invoices as $invoice) {
        if ($invoice['filename'] === $filename) {
            $file_found = true;
            break;
        }
    }
    
    if ($file_found) {
        try {
            $ftp = new FTPManager();
            $ftp->connect();
            
            $remote_file = rtrim($client_data['ftp_folder_path'], '/') . '/' . $filename;
            $temp_file = tempnam(sys_get_temp_dir(), 'invoice_');
            
            if ($ftp->downloadFile($remote_file, $temp_file)) {
                // Set headers for file download
                header('Content-Type: application/pdf');
                header('Content-Disposition: attachment; filename="' . $filename . '"');
                header('Content-Length: ' . filesize($temp_file));
                
                // Output file
                readfile($temp_file);
                
                // Clean up
                unlink($temp_file);
                $ftp->disconnect();
                exit();
            } else {
                $error_message = 'Failed to download file.';
            }
            
            $ftp->disconnect();
        } catch (Exception $e) {
            $error_message = 'Download failed: ' . $e->getMessage();
        }
    } else {
        $error_message = 'File not found or access denied.';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoices - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="dashboard">
    <header class="header">
        <div class="header-content">
            <h1><?php echo APP_NAME; ?> - Invoices</h1>
            <div class="user-info">
                <a href="../dashboard.php" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                <a href="../logout.php" class="btn btn-sm btn-danger">Logout</a>
            </div>
        </div>
    </header>
    
    <main class="main-content">
        <div class="module-header">
            <h2>📄 Invoices</h2>
            <p>View and manage your invoices. Click download to save a copy or pay now to make a payment.</p>
        </div>
        
        <?php if ($error_message): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <div class="invoices-section">
            <?php if (empty($invoices)): ?>
                <div class="empty-state">
                    <div class="empty-icon">📄</div>
                    <h3>No Invoices Found</h3>
                    <p>There are currently no invoice files available for your account.</p>
                    <p>Invoice files will appear here once they are uploaded to your client folder.</p>
                </div>
            <?php else: ?>
                <div class="table-container">
                    <div class="table-header">
                        <h3>Available Invoices (<?php echo count($invoices); ?>)</h3>
                        <button onclick="refreshInvoices()" class="btn btn-sm btn-secondary">🔄 Refresh</button>
                    </div>
                    
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Invoice File</th>
                                <th>File Size</th>
                                <th>Date Modified</th>
                                <th>Payment Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($invoices as $invoice): ?>
                                <?php
                                // Check payment status
                                $stmt = $db->prepare("SELECT payment_status FROM payments WHERE invoice_name = ? AND client_id = ? ORDER BY created_at DESC LIMIT 1");
                                $stmt->execute([$invoice['filename'], $_SESSION['client_id']]);
                                $payment = $stmt->fetch();
                                $payment_status = $payment ? $payment['payment_status'] : 'unpaid';
                                ?>
                                <tr>
                                    <td>
                                        <div class="invoice-info">
                                            <strong><?php echo htmlspecialchars($invoice['filename']); ?></strong>
                                        </div>
                                    </td>
                                    <td><?php echo formatFileSize($invoice['size']); ?></td>
                                    <td><?php echo date('Y-m-d H:i', $invoice['modified']); ?></td>
                                    <td>
                                        <span class="payment-status status-<?php echo $payment_status; ?>">
                                            <?php echo ucfirst($payment_status); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="?download=<?php echo urlencode($invoice['filename']); ?>" 
                                               class="btn btn-sm btn-secondary" title="Download Invoice">
                                                📥 Download
                                            </a>
                                            
                                            <?php if ($payment_status !== 'completed'): ?>
                                                <a href="payment.php?invoice=<?php echo urlencode($invoice['filename']); ?>" 
                                                   class="btn btn-sm btn-primary" title="Pay Now">
                                                    💳 Pay Now
                                                </a>
                                            <?php else: ?>
                                                <span class="btn btn-sm btn-success disabled" title="Already Paid">
                                                    ✅ Paid
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="invoice-info-section">
            <h3>Invoice Information</h3>
            <div class="info-grid">
                <div class="info-card">
                    <h4>📁 Client Folder</h4>
                    <p><?php echo htmlspecialchars($client_data['ftp_folder_path']); ?></p>
                </div>
                
                <div class="info-card">
                    <h4>📊 Payment Summary</h4>
                    <p>
                        <?php
                        $paid_count = 0;
                        $unpaid_count = 0;
                        foreach ($invoices as $invoice) {
                            $stmt = $db->prepare("SELECT payment_status FROM payments WHERE invoice_name = ? AND client_id = ? ORDER BY created_at DESC LIMIT 1");
                            $stmt->execute([$invoice['filename'], $_SESSION['client_id']]);
                            $payment = $stmt->fetch();
                            $status = $payment ? $payment['payment_status'] : 'unpaid';
                            
                            if ($status === 'completed') {
                                $paid_count++;
                            } else {
                                $unpaid_count++;
                            }
                        }
                        ?>
                        <strong>Paid:</strong> <?php echo $paid_count; ?><br>
                        <strong>Unpaid:</strong> <?php echo $unpaid_count; ?>
                    </p>
                </div>
            </div>
        </div>
    </main>
    
    <script src="../assets/js/script.js"></script>
    <script>
        function refreshInvoices() {
            window.location.reload();
        }
    </script>
</body>
</html>

<?php
/**
 * Format file size
 */
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>

<style>
.module-header {
    margin-bottom: 2rem;
}

.module-header h2 {
    color: #333;
    margin-bottom: 0.5rem;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-state h3 {
    color: #666;
    margin-bottom: 1rem;
}

.empty-state p {
    color: #888;
    margin-bottom: 0.5rem;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px 10px 0 0;
}

.invoice-info {
    display: flex;
    flex-direction: column;
}

.payment-status {
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.75rem;
    text-transform: uppercase;
    font-weight: 600;
}

.status-completed {
    background-color: #28a745;
    color: white;
}

.status-pending {
    background-color: #ffc107;
    color: #212529;
}

.status-unpaid {
    background-color: #dc3545;
    color: white;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.invoice-info-section {
    margin-top: 2rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.info-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-card h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.info-card p {
    color: #666;
    margin: 0;
}
</style>
