<?php
/**
 * Accounting Module
 * Placeholder for accounting functionality
 */

require_once '../config/config.php';
require_once '../classes/User.php';

// Require login and accounting module access
requireLogin();

$database = new Database();
$db = $database->getConnection();
$user = new User($db);

// Check if user has access to accounting module
$user_rights = $user->getUserRights($_SESSION['user_id']);
if (!in_array('accounting', $user_rights)) {
    header('Location: ../dashboard.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accounting - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="dashboard">
    <header class="header">
        <div class="header-content">
            <h1><?php echo APP_NAME; ?> - Accounting</h1>
            <div class="user-info">
                <a href="../dashboard.php" class="btn btn-sm btn-secondary">Back to Dashboard</a>
                <a href="../logout.php" class="btn btn-sm btn-danger">Logout</a>
            </div>
        </div>
    </header>
    
    <main class="main-content">
        <div class="module-header">
            <h2>💰 Accounting</h2>
            <p>Financial management and reporting</p>
        </div>
        
        <div class="placeholder-content">
            <div class="placeholder-card">
                <div class="placeholder-icon">💰</div>
                <h3>Accounting Module</h3>
                <p>This module is under development and will include:</p>
                <ul>
                    <li>Financial reporting and analytics</li>
                    <li>Invoice and payment tracking</li>
                    <li>Expense management</li>
                    <li>Profit and loss statements</li>
                    <li>Tax calculations and reporting</li>
                    <li>Budget planning and forecasting</li>
                </ul>
                <p><strong>Coming Soon!</strong></p>
            </div>
        </div>
    </main>
    
    <script src="../assets/js/script.js"></script>
</body>
</html>

<style>
.placeholder-content {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
}

.placeholder-card {
    background: white;
    padding: 3rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-align: center;
    max-width: 500px;
}

.placeholder-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.placeholder-card h3 {
    color: #333;
    margin-bottom: 1rem;
}

.placeholder-card p {
    color: #666;
    margin-bottom: 1rem;
}

.placeholder-card ul {
    text-align: left;
    color: #666;
    margin: 1rem 0;
}

.placeholder-card li {
    margin-bottom: 0.5rem;
}
</style>
