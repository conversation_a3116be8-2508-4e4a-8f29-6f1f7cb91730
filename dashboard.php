<?php
/**
 * Dashboard Page
 * Main dashboard showing available modules based on user rights
 */

require_once 'config/config.php';
require_once 'classes/User.php';

// Require login
requireLogin();

// Get user rights
$database = new Database();
$db = $database->getConnection();
$user = new User($db);
$user_rights = $user->getUserRights($_SESSION['user_id']);

// Define module information
$modules = [
    'invoices' => [
        'title' => 'Invoices',
        'description' => 'View, manage and process client invoices with integrated payment solutions',
        'icon' => 'invoices',
        'url' => 'modules/invoices.php',
        'color' => '#3b82f6',
        'stats' => ['Total Invoices', 'Pending Payments', 'This Month']
    ],
    'freight' => [
        'title' => 'Freight System',
        'description' => 'Comprehensive freight management and logistics operations',
        'icon' => 'freight',
        'url' => 'modules/freight.php',
        'color' => '#10b981',
        'stats' => ['Active Shipments', 'Delivered', 'In Transit']
    ],
    'tariff' => [
        'title' => 'Tariff Book',
        'description' => 'Access comprehensive tariff information and customs data',
        'icon' => 'tariff',
        'url' => 'modules/tariff.php',
        'color' => '#f59e0b',
        'stats' => ['Tariff Codes', 'Updated Today', 'Categories']
    ],
    'accounting' => [
        'title' => 'Accounting',
        'description' => 'Complete financial management and accounting operations',
        'icon' => 'accounting',
        'url' => 'modules/accounting.php',
        'color' => '#8b5cf6',
        'stats' => ['Transactions', 'Balance', 'Reports']
    ],
    'backoffice' => [
        'title' => 'Back Office',
        'description' => 'Administrative functions and system management tools',
        'icon' => 'backoffice',
        'url' => 'modules/backoffice.php',
        'color' => '#ef4444',
        'stats' => ['Users', 'Settings', 'Logs']
    ]
];

// Filter modules based on user rights
$available_modules = [];
foreach ($modules as $key => $module) {
    if (in_array($key, $user_rights)) {
        $available_modules[$key] = $module;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="dashboard">
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <div class="brand-section">
                    <div class="brand-icon">
                        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="32" height="32" rx="8" fill="rgba(255,255,255,0.2)"/>
                            <path d="M8 12h16v2H8v-2zm0 4h16v2H8v-2zm0 4h12v2H8v-2z" fill="white"/>
                            <circle cx="20" cy="20" r="3" fill="#2B5E5F"/>
                        </svg>
                    </div>
                    <div class="brand-text">
                        <h1><?php echo APP_NAME; ?></h1>
                        <span class="brand-subtitle">Executive Portal</span>
                    </div>
                </div>
                <nav class="main-navigation">
                    <a href="dashboard.php" class="nav-item active">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8 1l6 6v8H2V7l6-6z"/>
                        </svg>
                        Dashboard
                    </a>
                    <?php if (in_array('invoices', $user_rights)): ?>
                    <a href="modules/invoices.php" class="nav-item">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M3 2h10a1 1 0 011 1v10a1 1 0 01-1 1H3a1 1 0 01-1-1V3a1 1 0 011-1zm1 2v8h8V4H4z"/>
                        </svg>
                        Invoices
                    </a>
                    <?php endif; ?>
                    <?php if (in_array('accounting', $user_rights)): ?>
                    <a href="modules/accounting.php" class="nav-item">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8 1a7 7 0 100 14A7 7 0 008 1zM7 4h2v2H7V4zm0 4h2v4H7V8z"/>
                        </svg>
                        Accounting
                    </a>
                    <?php endif; ?>
                </nav>
            </div>

            <div class="header-right">
                <div class="header-actions">
                    <button class="action-btn notification-btn" title="Notifications">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"/>
                        </svg>
                        <span class="notification-badge">3</span>
                    </button>

                    <button class="action-btn settings-btn" title="Settings">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"/>
                        </svg>
                    </button>
                </div>

                <div class="user-profile">
                    <div class="user-avatar">
                        <span><?php echo strtoupper(substr($_SESSION['user_name'], 0, 2)); ?></span>
                    </div>
                    <div class="user-details">
                        <div class="user-name"><?php echo htmlspecialchars($_SESSION['user_name']); ?></div>
                        <div class="user-meta">
                            <span class="client-name"><?php echo htmlspecialchars($_SESSION['client_name']); ?></span>
                            <span class="role-badge <?php echo $_SESSION['role']; ?>"><?php echo ucfirst($_SESSION['role']); ?></span>
                        </div>
                    </div>
                    <div class="user-actions">
                        <?php if (isAdmin()): ?>
                            <a href="admin/users.php" class="btn btn-sm btn-secondary">
                                <svg width="14" height="14" viewBox="0 0 14 14" fill="currentColor">
                                    <path d="M7 1a3 3 0 100 6 3 3 0 000-6zM1 12a6 6 0 1112 0H1z"/>
                                </svg>
                                Admin
                            </a>
                        <?php endif; ?>
                        <a href="logout.php" class="btn btn-sm btn-danger">
                            <svg width="14" height="14" viewBox="0 0 14 14" fill="currentColor">
                                <path d="M5 1a1 1 0 000 2h4a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 100 2h4a3 3 0 003-3V4a3 3 0 00-3-3H5z"/>
                                <path d="M3 7a1 1 0 000 2h6l-2 2a1 1 0 001.414 1.414l3.5-3.5a1 1 0 000-1.414l-3.5-3.5A1 1 0 007 4.586L9 6.586H3z"/>
                            </svg>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>
    
    <main class="main-content">
        <div class="welcome-section">
            <h2>Dashboard</h2>
            <p>Welcome to your portal dashboard. Select a module below to get started.</p>
        </div>
        
        <?php if (empty($available_modules)): ?>
            <div class="alert alert-info">
                <strong>No modules available.</strong> Please contact your administrator to assign module access rights.
            </div>
        <?php else: ?>
            <div class="modules-grid">
                <?php foreach ($available_modules as $key => $module): ?>
                    <a href="<?php echo $module['url']; ?>" class="module-card" data-module="<?php echo $key; ?>">
                        <div class="module-header">
                            <div class="module-icon" style="--module-color: <?php echo $module['color']; ?>">
                                <?php
                                switch($module['icon']) {
                                    case 'invoices':
                                        echo '<svg width="32" height="32" viewBox="0 0 32 32" fill="currentColor">
                                                <path d="M6 4a2 2 0 00-2 2v20a2 2 0 002 2h20a2 2 0 002-2V6a2 2 0 00-2-2H6zm2 4h16v2H8V8zm0 4h16v2H8v-2zm0 4h12v2H8v-2zm0 4h10v2H8v-2z"/>
                                              </svg>';
                                        break;
                                    case 'accounting':
                                        echo '<svg width="32" height="32" viewBox="0 0 32 32" fill="currentColor">
                                                <path d="M16 2C8.268 2 2 8.268 2 16s6.268 14 14 14 14-6.268 14-14S23.732 2 16 2zm0 6a2 2 0 110 4 2 2 0 010-4zm-2 6h4v8h-4v-8z"/>
                                              </svg>';
                                        break;
                                    case 'freight':
                                        echo '<svg width="32" height="32" viewBox="0 0 32 32" fill="currentColor">
                                                <path d="M4 8a2 2 0 012-2h12a2 2 0 012 2v4h4l4 4v8a2 2 0 01-2 2h-2a4 4 0 11-8 0H12a4 4 0 11-8 0H2a2 2 0 01-2-2V8zm18 12a2 2 0 100 4 2 2 0 000-4zM8 20a2 2 0 100 4 2 2 0 000-4z"/>
                                              </svg>';
                                        break;
                                    case 'tariff':
                                        echo '<svg width="32" height="32" viewBox="0 0 32 32" fill="currentColor">
                                                <path d="M4 4a2 2 0 00-2 2v20a2 2 0 002 2h24a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 4h20v4H6V8zm0 6h8v4H6v-4zm10 0h10v4H16v-4zm-10 6h8v4H6v-4zm10 0h10v4H16v-4z"/>
                                              </svg>';
                                        break;
                                    case 'backoffice':
                                        echo '<svg width="32" height="32" viewBox="0 0 32 32" fill="currentColor">
                                                <path d="M4 6a2 2 0 012-2h20a2 2 0 012 2v20a2 2 0 01-2 2H6a2 2 0 01-2-2V6zm4 2v16h16V8H8zm4 4h8v2h-8v-2zm0 4h6v2h-6v-2z"/>
                                              </svg>';
                                        break;
                                    default:
                                        echo '<svg width="32" height="32" viewBox="0 0 32 32" fill="currentColor">
                                                <path d="M16 2C8.268 2 2 8.268 2 16s6.268 14 14 14 14-6.268 14-14S23.732 2 16 2z"/>
                                              </svg>';
                                }
                                ?>
                            </div>
                            <div class="module-status">
                                <span class="status-indicator active"></span>
                            </div>
                        </div>

                        <div class="module-content">
                            <div class="module-title"><?php echo $module['title']; ?></div>
                            <div class="module-description"><?php echo $module['description']; ?></div>
                        </div>

                        <div class="module-footer">
                            <div class="module-stats">
                                <?php foreach ($module['stats'] as $index => $stat): ?>
                                    <div class="stat-item">
                                        <span class="stat-value"><?php echo rand(10, 999); ?></span>
                                        <span class="stat-label"><?php echo $stat; ?></span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="module-action">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                    <path d="M8.636 3.5a.5.5 0 0 0-.5-.5H1.5A1.5 1.5 0 0 0 0 4.5v10A1.5 1.5 0 0 0 1.5 16h10a1.5 1.5 0 0 0 1.5-1.5V7.864a.5.5 0 0 0-.5-.5C11.864 7.364 8.636 4.136 8.636 3.5z"/>
                                    <path d="M16 .5a.5.5 0 0 0-.5-.5h-5a.5.5 0 0 0 0 1h3.793L6.146 9.146a.5.5 0 1 0 .708.708L15 1.707V5.5a.5.5 0 0 0 1 0v-5z"/>
                                </svg>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <div class="dashboard-analytics">
            <div class="analytics-header">
                <h3>Business Analytics</h3>
                <div class="analytics-controls">
                    <select class="period-selector">
                        <option value="7">Last 7 days</option>
                        <option value="30" selected>Last 30 days</option>
                        <option value="90">Last 90 days</option>
                        <option value="365">Last year</option>
                    </select>
                    <button class="refresh-btn" title="Refresh Data">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8 3a5 5 0 104.546 2.914.5.5 0 00-.908-.417A4 4 0 118 4v1z"/>
                            <path d="M8 4.466V.534a.25.25 0 01.41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 018 4.466z"/>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="analytics-grid">
                <div class="stat-card primary">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div class="stat-trend positive">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8.5 4.5a.5.5 0 0 0-1 0v3h-3a.5.5 0 0 0 0 1h3v3a.5.5 0 0 0 1 0v-3h3a.5.5 0 0 0 0-1h-3v-3z"/>
                            </svg>
                            <span>+12%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">
                            <?php
                            // Get invoice count for this client
                            $stmt = $db->prepare("SELECT COUNT(*) as count FROM payments WHERE client_id = ? AND payment_status = 'completed'");
                            $stmt->execute([$_SESSION['client_id']]);
                            $paid_invoices = $stmt->fetch()['count'];
                            echo $paid_invoices;
                            ?>
                        </div>
                        <div class="stat-label">Paid Invoices</div>
                        <div class="stat-description">Successfully processed payments</div>
                    </div>
                    <div class="stat-progress">
                        <div class="progress-bar" style="width: 75%"></div>
                    </div>
                </div>

                <div class="stat-card warning">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"/>
                            </svg>
                        </div>
                        <div class="stat-trend negative">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z"/>
                            </svg>
                            <span>-3%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">
                            <?php
                            // Get pending payments count
                            $stmt = $db->prepare("SELECT COUNT(*) as count FROM payments WHERE client_id = ? AND payment_status = 'pending'");
                            $stmt->execute([$_SESSION['client_id']]);
                            $pending_payments = $stmt->fetch()['count'];
                            echo $pending_payments;
                            ?>
                        </div>
                        <div class="stat-label">Pending Payments</div>
                        <div class="stat-description">Awaiting payment processing</div>
                    </div>
                    <div class="stat-progress">
                        <div class="progress-bar" style="width: 45%"></div>
                    </div>
                </div>

                <div class="stat-card success">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.9 1 3 1.9 3 3V21C3 22.1 3.9 23 5 23H19C20.1 23 21 22.1 21 21V9Z"/>
                            </svg>
                        </div>
                        <div class="stat-trend positive">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8.5 4.5a.5.5 0 0 0-1 0v3h-3a.5.5 0 0 0 0 1h3v3a.5.5 0 0 0 1 0v-3h3a.5.5 0 0 0 0-1h-3v-3z"/>
                            </svg>
                            <span>+8%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">
                            <?php
                            // Get total amount paid this month
                            $stmt = $db->prepare("SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE client_id = ? AND payment_status = 'completed' AND MONTH(created_at) = MONTH(NOW())");
                            $stmt->execute([$_SESSION['client_id']]);
                            $monthly_total = $stmt->fetch()['total'];
                            echo 'R' . number_format($monthly_total, 2);
                            ?>
                        </div>
                        <div class="stat-label">Monthly Revenue</div>
                        <div class="stat-description">Total earnings this month</div>
                    </div>
                    <div class="stat-progress">
                        <div class="progress-bar" style="width: 85%"></div>
                    </div>
                </div>

                <div class="stat-card info">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div class="stat-trend neutral">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                            </svg>
                            <span>0%</span>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">
                            <?php
                            // Get active modules count
                            echo count($available_modules);
                            ?>
                        </div>
                        <div class="stat-label">Active Modules</div>
                        <div class="stat-description">Available system modules</div>
                    </div>
                    <div class="stat-progress">
                        <div class="progress-bar" style="width: 60%"></div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner-ring"></div>
            <div class="loading-text">Loading Dashboard...</div>
        </div>
    </div>

    <script src="assets/js/script.js"></script>
    <script>
        // Enhanced Dashboard Interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Hide loading overlay
            const loadingOverlay = document.getElementById('loading-overlay');
            setTimeout(() => {
                loadingOverlay.style.opacity = '0';
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                }, 300);
            }, 800);

            // Simple module card interactions (no animations)
            const moduleCards = document.querySelectorAll('.module-card');
            moduleCards.forEach(card => {
                // Just ensure cards are clickable - no special effects
                card.style.cursor = 'pointer';
            });

            // Animate progress bars
            const progressBars = document.querySelectorAll('.progress-bar');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const bar = entry.target;
                        const width = bar.style.width;
                        bar.style.width = '0%';
                        setTimeout(() => {
                            bar.style.width = width;
                        }, 100);
                    }
                });
            });

            progressBars.forEach(bar => observer.observe(bar));

            // Add refresh functionality
            const refreshBtn = document.querySelector('.refresh-btn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function() {
                    this.style.transform = 'rotate(360deg)';
                    setTimeout(() => {
                        this.style.transform = 'rotate(0deg)';
                    }, 500);
                });
            }
        });
    </script>
</body>
</html>


