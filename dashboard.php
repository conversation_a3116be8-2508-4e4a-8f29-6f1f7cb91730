<?php
/**
 * Dashboard Page
 * Main dashboard showing available modules based on user rights
 */

require_once 'config/config.php';
require_once 'classes/User.php';

// Require login
requireLogin();

// Get user rights
$database = new Database();
$db = $database->getConnection();
$user = new User($db);
$user_rights = $user->getUserRights($_SESSION['user_id']);

// Define module information
$modules = [
    'invoices' => [
        'title' => 'Invoices',
        'description' => 'View and manage invoices',
        'icon' => '📄',
        'url' => 'modules/invoices.php'
    ],
    'freight' => [
        'title' => 'Freight System',
        'description' => 'Manage freight operations',
        'icon' => '🚛',
        'url' => 'modules/freight.php'
    ],
    'tariff' => [
        'title' => 'Tariff Book',
        'description' => 'View tariff information',
        'icon' => '📊',
        'url' => 'modules/tariff.php'
    ],
    'accounting' => [
        'title' => 'Accounting',
        'description' => 'Financial management',
        'icon' => '💰',
        'url' => 'modules/accounting.php'
    ],
    'backoffice' => [
        'title' => 'Back Office',
        'description' => 'Administrative functions',
        'icon' => '🏢',
        'url' => 'modules/backoffice.php'
    ]
];

// Filter modules based on user rights
$available_modules = [];
foreach ($modules as $key => $module) {
    if (in_array($key, $user_rights)) {
        $available_modules[$key] = $module;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="dashboard">
    <header class="header">
        <div class="header-content">
            <h1><?php echo APP_NAME; ?></h1>
            <div class="user-info">
                <span>Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <span class="separator">|</span>
                <span><?php echo htmlspecialchars($_SESSION['client_name']); ?></span>
                <span class="separator">|</span>
                <span class="role"><?php echo ucfirst($_SESSION['role']); ?></span>
                <span class="separator">|</span>
                <?php if (isAdmin()): ?>
                    <a href="admin/users.php" class="btn btn-sm btn-secondary">Admin Panel</a>
                <?php endif; ?>
                <a href="logout.php" class="btn btn-sm btn-danger">Logout</a>
            </div>
        </div>
    </header>
    
    <main class="main-content">
        <div class="welcome-section">
            <h2>Dashboard</h2>
            <p>Welcome to your portal dashboard. Select a module below to get started.</p>
        </div>
        
        <?php if (empty($available_modules)): ?>
            <div class="alert alert-info">
                <strong>No modules available.</strong> Please contact your administrator to assign module access rights.
            </div>
        <?php else: ?>
            <div class="modules-grid">
                <?php foreach ($available_modules as $key => $module): ?>
                    <a href="<?php echo $module['url']; ?>" class="module-card">
                        <div class="module-icon"><?php echo $module['icon']; ?></div>
                        <div class="module-title"><?php echo $module['title']; ?></div>
                        <div class="module-description"><?php echo $module['description']; ?></div>
                    </a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <div class="dashboard-stats">
            <h3>Quick Stats</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">
                        <?php
                        // Get invoice count for this client
                        $stmt = $db->prepare("SELECT COUNT(*) as count FROM payments WHERE client_id = ? AND payment_status = 'completed'");
                        $stmt->execute([$_SESSION['client_id']]);
                        $paid_invoices = $stmt->fetch()['count'];
                        echo $paid_invoices;
                        ?>
                    </div>
                    <div class="stat-label">Paid Invoices</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">
                        <?php
                        // Get pending payments count
                        $stmt = $db->prepare("SELECT COUNT(*) as count FROM payments WHERE client_id = ? AND payment_status = 'pending'");
                        $stmt->execute([$_SESSION['client_id']]);
                        $pending_payments = $stmt->fetch()['count'];
                        echo $pending_payments;
                        ?>
                    </div>
                    <div class="stat-label">Pending Payments</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">
                        <?php
                        // Get total amount paid this month
                        $stmt = $db->prepare("SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE client_id = ? AND payment_status = 'completed' AND MONTH(created_at) = MONTH(NOW())");
                        $stmt->execute([$_SESSION['client_id']]);
                        $monthly_total = $stmt->fetch()['total'];
                        echo 'R' . number_format($monthly_total, 2);
                        ?>
                    </div>
                    <div class="stat-label">This Month</div>
                </div>
            </div>
        </div>
    </main>
    
    <script src="assets/js/script.js"></script>
</body>
</html>


